<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Grid Overlay Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-controls button {
            margin: 5px;
            padding: 10px 15px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-controls button:hover {
            background: #005a87;
        }
        .image-container {
            position: relative;
            max-width: 90vw;
            max-height: 70vh;
        }
        img {
            max-width: 100%;
            max-height: 100%;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
    </style>
    <!-- Include the extension's CSS -->
    <link rel="stylesheet" href="artist-overlay-grid-v2/overlay.css">
</head>
<body>
    <div class="test-controls">
        <h3>Simple Grid Overlay Test</h3>
        <button onclick="createGrid()">Create Grid</button>
        <button onclick="toggleGrid()">Toggle Grid</button>
        <button onclick="removeGrid()">Remove Grid</button>
        <button onclick="debugGrid()">Debug Grid</button>
    </div>
    
    <div class="image-container">
        <img src="https://picsum.photos/800/600" alt="Test Image for Grid Overlay" />
    </div>

    <script>
        let gridOverlay = null;
        
        function createGrid() {
            console.log('Creating grid overlay...');
            
            // Remove existing overlay if any
            const existing = document.getElementById('artist-grid-overlay');
            if (existing) {
                existing.remove();
            }
            
            // Create overlay element
            gridOverlay = document.createElement('div');
            gridOverlay.id = 'artist-grid-overlay';
            gridOverlay.classList.add('active');
            gridOverlay.classList.add('file-context');
            
            // Create grid lines
            const gridLines = document.createElement('div');
            gridLines.className = 'grid-lines';
            gridOverlay.appendChild(gridLines);
            
            // Apply CSS custom properties for grid
            gridLines.style.setProperty('--grid-color', '#ff0000');
            gridLines.style.setProperty('--grid-size-x', '50px');
            gridLines.style.setProperty('--grid-size-y', '50px');
            gridLines.style.setProperty('--grid-offset-x', '0px');
            gridLines.style.setProperty('--grid-offset-y', '0px');
            
            // Force visibility with inline styles for testing
            gridOverlay.style.display = 'block';
            gridOverlay.style.position = 'fixed';
            gridOverlay.style.top = '0';
            gridOverlay.style.left = '0';
            gridOverlay.style.width = '100vw';
            gridOverlay.style.height = '100vh';
            gridOverlay.style.zIndex = '2147483647';
            gridOverlay.style.pointerEvents = 'none';
            gridOverlay.style.opacity = '1';
            
            // Append to body
            document.body.appendChild(gridOverlay);
            
            console.log('Grid overlay created:', gridOverlay);
            console.log('Grid overlay classes:', gridOverlay.className);
            console.log('Grid overlay style:', gridOverlay.style.cssText);
        }
        
        function toggleGrid() {
            if (gridOverlay) {
                if (gridOverlay.classList.contains('active')) {
                    gridOverlay.classList.remove('active');
                    console.log('Grid hidden');
                } else {
                    gridOverlay.classList.add('active');
                    console.log('Grid shown');
                }
            } else {
                console.log('No grid overlay exists');
            }
        }
        
        function removeGrid() {
            if (gridOverlay) {
                gridOverlay.remove();
                gridOverlay = null;
                console.log('Grid removed');
            } else {
                console.log('No grid to remove');
            }
        }
        
        function debugGrid() {
            const overlay = document.getElementById('artist-grid-overlay');
            console.log('Debug Info:', {
                overlayExists: !!overlay,
                overlayClasses: overlay ? overlay.className : 'none',
                overlayStyle: overlay ? overlay.style.cssText : 'none',
                overlayComputedStyle: overlay ? window.getComputedStyle(overlay) : 'none'
            });
            
            if (overlay) {
                const gridLines = overlay.querySelector('.grid-lines');
                console.log('Grid Lines:', {
                    exists: !!gridLines,
                    style: gridLines ? gridLines.style.cssText : 'none',
                    computedStyle: gridLines ? window.getComputedStyle(gridLines) : 'none'
                });
            }
        }
    </script>
</body>
</html>
