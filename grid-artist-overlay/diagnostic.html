<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid Overlay Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagnostic-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .status.warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .status.info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Grid Overlay Extension Diagnostic</h1>
    
    <div class="diagnostic-section">
        <h3>Extension Status</h3>
        <div id="extension-status" class="status info">Checking extension status...</div>
        <button onclick="checkExtensionStatus()">Recheck Extension</button>
    </div>

    <div class="diagnostic-section">
        <h3>Content Script Status</h3>
        <div id="content-script-status" class="status info">Checking content script...</div>
        <button onclick="checkContentScript()">Test Content Script</button>
    </div>

    <div class="diagnostic-section">
        <h3>Grid Overlay Tests</h3>
        <div id="grid-status" class="status info">Ready to test grid...</div>
        <button onclick="testToggleGrid()">Test Toggle Grid</button>
        <button onclick="testGridSettings()">Test Grid Settings</button>
        <button onclick="checkGridElement()">Check Grid Element</button>
    </div>

    <div class="diagnostic-section">
        <h3>Debug Log</h3>
        <div id="debug-log" class="log">Diagnostic page loaded...<br></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            const colorMap = {
                'info': '#333',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            logElement.innerHTML += `<span style="color: ${colorMap[type]}">[${timestamp}] ${message}</span><br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[Diagnostic] ${message}`);
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = 'Log cleared...<br>';
        }

        async function checkExtensionStatus() {
            log('Checking if extension APIs are available...');
            
            if (typeof chrome === 'undefined') {
                updateStatus('extension-status', 'Chrome extension APIs not available', 'error');
                log('ERROR: Chrome extension APIs not available', 'error');
                return false;
            }

            if (!chrome.runtime) {
                updateStatus('extension-status', 'Chrome runtime API not available', 'error');
                log('ERROR: Chrome runtime API not available', 'error');
                return false;
            }

            updateStatus('extension-status', 'Extension APIs available', 'success');
            log('SUCCESS: Extension APIs are available', 'success');
            return true;
        }

        async function checkContentScript() {
            log('Testing content script communication...');
            
            try {
                // This will only work if we're in an extension context
                if (typeof chrome !== 'undefined' && chrome.tabs) {
                    const tabs = await new Promise((resolve) => {
                        chrome.tabs.query({ active: true, currentWindow: true }, resolve);
                    });
                    
                    if (tabs.length === 0) {
                        throw new Error('No active tab found');
                    }

                    const response = await new Promise((resolve, reject) => {
                        chrome.tabs.sendMessage(tabs[0].id, { action: 'ping' }, (response) => {
                            if (chrome.runtime.lastError) {
                                reject(new Error(chrome.runtime.lastError.message));
                            } else {
                                resolve(response);
                            }
                        });
                    });

                    updateStatus('content-script-status', 'Content script is responding', 'success');
                    log(`SUCCESS: Content script responded: ${JSON.stringify(response)}`, 'success');
                } else {
                    updateStatus('content-script-status', 'Cannot test - not in extension context', 'warning');
                    log('WARNING: Cannot test content script from this context', 'warning');
                }
            } catch (error) {
                updateStatus('content-script-status', `Content script error: ${error.message}`, 'error');
                log(`ERROR: Content script test failed: ${error.message}`, 'error');
            }
        }

        async function testToggleGrid() {
            log('Testing grid toggle...');
            // This would need to be run from the extension popup context
            updateStatus('grid-status', 'Grid toggle test requires extension popup context', 'warning');
            log('WARNING: Grid toggle test requires extension popup context', 'warning');
        }

        async function testGridSettings() {
            log('Testing grid settings...');
            updateStatus('grid-status', 'Grid settings test requires extension popup context', 'warning');
            log('WARNING: Grid settings test requires extension popup context', 'warning');
        }

        function checkGridElement() {
            log('Checking for grid overlay element in DOM...');
            
            const gridElement = document.getElementById('artist-grid-overlay');
            if (gridElement) {
                updateStatus('grid-status', 'Grid overlay element found in DOM', 'success');
                log(`SUCCESS: Grid element found. Display: ${gridElement.style.display}, Z-index: ${gridElement.style.zIndex}`, 'success');
            } else {
                updateStatus('grid-status', 'Grid overlay element not found in DOM', 'error');
                log('ERROR: Grid overlay element not found in DOM', 'error');
            }
        }

        // Auto-run initial checks
        window.addEventListener('load', () => {
            log('Diagnostic page loaded');
            setTimeout(checkExtensionStatus, 500);
            setTimeout(checkContentScript, 1000);
            setTimeout(checkGridElement, 1500);
        });
    </script>
</body>
</html>
