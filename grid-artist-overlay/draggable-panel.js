// Draggable configuration panel that overlays on the page
// Check if class already exists to prevent re-declaration errors
if (!window.DraggableConfigPanel) {
window.DraggableConfigPanel = class DraggableConfigPanel {
  constructor() {
    this.panel = null;
    this.isVisible = false;
    this.isCollapsed = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.position = { x: 0, y: 80 }; // Default position - aligned to left edge
    this.clickStartTime = 0;
    this.lockedBaseX = 50; // Store the base X value when locking is enabled
    this.lockedBaseY = 50; // Store the base Y value when locking is enabled

    this.createPanel();
    this.attachEventListeners();
  }
  
  createPanel() {
    // Create the main panel container
    this.panel = document.createElement('div');
    this.panel.id = 'grid-config-panel';
    this.panel.innerHTML = `
      <div class="panel-header" id="panel-header">
        <span>Grid Overlay Settings</span>
        <span class="accordion-arrow" id="accordion-arrow">▼</span>
      </div>
      <div class="panel-content" id="panel-content">
        <div class="control-group">
          <label for="panel-grid-color">Grid Color</label>
          <input type="color" id="panel-grid-color" value="#000000">
        </div>
        
        <div class="control-group">
          <label for="panel-grid-size-x">Grid Size X 
            <span class="range-value" id="panel-grid-size-x-value">50</span>px
          </label>
          <input type="range" id="panel-grid-size-x" min="10" max="200" value="50">
        </div>
        
        <div class="control-group">
          <label for="panel-grid-size-y">Grid Size Y 
            <span class="range-value" id="panel-grid-size-y-value">50</span>px
          </label>
          <input type="range" id="panel-grid-size-y" min="10" max="200" value="50">
        </div>
        
        <div class="control-group">
          <label for="panel-lock-grid-sizes">
            <input type="checkbox" id="panel-lock-grid-sizes"> Lock Grid Sizes
          </label>
        </div>
        
        <div class="control-group">
          <label for="panel-grid-opacity">Opacity
            <span class="range-value" id="panel-grid-opacity-value">20%</span>
          </label>
          <input type="range" id="panel-grid-opacity" min="0.1" max="1" step="0.1" value="0.2">
        </div>
        
        <div class="control-group">
          <label for="panel-horizontal-offset">Horizontal Offset 
            <span class="range-value" id="panel-horizontal-offset-value">0</span>px
          </label>
          <input type="range" id="panel-horizontal-offset" min="-100" max="100" value="0">
        </div>
        
        <div class="control-group">
          <label for="panel-vertical-offset">Vertical Offset 
            <span class="range-value" id="panel-vertical-offset-value">0</span>px
          </label>
          <input type="range" id="panel-vertical-offset" min="-100" max="100" value="0">
        </div>
        
        <div class="control-group">
          <button id="panel-toggle-grid">Toggle Grid</button>
          <button id="panel-close" style="margin-left: 10px; background: #dc3545;">Close Panel</button>
        </div>
      </div>
    `;
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      #grid-config-panel {
        position: fixed;
        top: 80px;
        left: 0px;
        width: 280px;
        background: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 2147483647;
        font-family: Arial, sans-serif;
        font-size: 14px;
        transition: height 0.3s ease;
        user-select: none;
        pointer-events: auto;
        overflow: hidden;
        /* Force 100% scale regardless of browser zoom */
        zoom: 1 !important;
        transform: none !important;
        transform-origin: 0 0 !important;
      }

      #grid-config-panel.collapsed {
        height: 45px !important;
      }
      
      #grid-config-panel .panel-header {
        background: #007bff;
        color: white;
        padding: 10px 15px;
        border-radius: 8px 8px 0 0;
        cursor: move;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
        height: 25px;
      }

      .accordion-arrow {
        font-size: 12px;
        transition: transform 0.3s ease;
        cursor: pointer;
        user-select: none;
        margin-left: 10px;
      }

      #grid-config-panel.collapsed .accordion-arrow {
        transform: rotate(-90deg);
      }
      
      #grid-config-panel .panel-content {
        padding: 15px;
        max-height: 400px;
        overflow-y: auto;
      }
      

      
      #grid-config-panel .control-group {
        margin-bottom: 12px;
      }
      
      #grid-config-panel .control-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
      }
      
      #grid-config-panel .control-group input[type="range"] {
        width: 100%;
      }
      
      #grid-config-panel .control-group input[type="color"] {
        width: 50px;
        height: 30px;
        border: none;
        border-radius: 4px;
      }
      
      #grid-config-panel .control-group button {
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
      }
      
      #grid-config-panel .control-group button:hover {
        background: #0056b3;
      }
      
      #grid-config-panel .range-value {
        font-weight: bold;
        color: #007bff;
      }
      
      #grid-config-panel .control-group input[type="checkbox"] {
        margin-right: 8px;
        transform: scale(1.2);
      }

      /* Force all panel children to ignore browser zoom */
      #grid-config-panel *,
      #grid-config-panel *:before,
      #grid-config-panel *:after {
        zoom: 1 !important;
        transform-origin: 0 0 !important;
      }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(this.panel);

    // Apply zoom compensation to keep panel size consistent regardless of zoom
    this.applyZoomCompensation();

    // Monitor zoom changes and reapply compensation
    this.startZoomMonitoring();
  }
  
  attachEventListeners() {
    const header = this.panel.querySelector('#panel-header');
    const closeBtn = this.panel.querySelector('#panel-close');
    const accordionArrow = this.panel.querySelector('#accordion-arrow');

    // Dragging and accordion functionality
    header.addEventListener('mousedown', (e) => {
      this.clickStartTime = Date.now();
      this.isDragging = true;
      const rect = this.panel.getBoundingClientRect();
      this.dragOffset.x = e.clientX - rect.left;
      this.dragOffset.y = e.clientY - rect.top;

      document.addEventListener('mousemove', this.handleDrag.bind(this));
      document.addEventListener('mouseup', this.stopDrag.bind(this));
      e.preventDefault();
    });

    // Close panel
    closeBtn.addEventListener('click', () => {
      this.hide();
    });

    // Sync controls with grid overlay
    this.syncControlsWithGrid();
  }
  
  handleDrag(e) {
    if (!this.isDragging) return;

    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;

    // Keep panel within viewport bounds
    // Allow alignment to left edge (0px) but keep some margin on right
    const rightMargin = 10;
    const minY = 60; // Keep below browser toolbar area
    const maxX = window.innerWidth - this.panel.offsetWidth - rightMargin;
    const maxY = window.innerHeight - this.panel.offsetHeight - 10;

    this.position.x = Math.max(0, Math.min(x, maxX)); // Allow 0px left alignment
    this.position.y = Math.max(minY, Math.min(y, maxY));

    this.panel.style.left = this.position.x + 'px';
    this.panel.style.top = this.position.y + 'px';
  }
  
  stopDrag() {
    const clickDuration = Date.now() - this.clickStartTime;

    // If it was a quick click (not a drag), toggle accordion
    if (clickDuration < 200) {
      this.toggleAccordion();
    }

    this.isDragging = false;
    document.removeEventListener('mousemove', this.handleDrag.bind(this));
    document.removeEventListener('mouseup', this.stopDrag.bind(this));
  }

  toggleAccordion() {
    this.isCollapsed = !this.isCollapsed;
    this.panel.classList.toggle('collapsed', this.isCollapsed);

    // Update cursor style - no move cursor when collapsed
    const header = this.panel.querySelector('#panel-header');
    header.style.cursor = this.isCollapsed ? 'pointer' : 'move';
  }
  

  
  show() {
    this.isVisible = true;
    this.panel.style.display = 'block';
    this.panel.style.left = this.position.x + 'px';
    this.panel.style.top = this.position.y + 'px';

    // Apply zoom compensation when showing
    this.applyZoomCompensation();

    // Sync with current grid settings
    this.loadCurrentSettings();
  }

  loadCurrentSettings() {
    // Ensure grid overlay is initialized
    if (!window.gridOverlay && window.initializeGridOverlay) {
      console.log('Grid overlay not found, initializing...');
      window.initializeGridOverlay();
    }

    if (window.gridOverlay && window.gridOverlay.settings) {
      const settings = window.gridOverlay.settings;

      // Update all controls with current values
      const colorInput = this.panel.querySelector('#panel-grid-color');
      const sizeXInput = this.panel.querySelector('#panel-grid-size-x');
      const sizeYInput = this.panel.querySelector('#panel-grid-size-y');
      const lockInput = this.panel.querySelector('#panel-lock-grid-sizes');
      const opacityInput = this.panel.querySelector('#panel-grid-opacity');
      const offsetXInput = this.panel.querySelector('#panel-horizontal-offset');
      const offsetYInput = this.panel.querySelector('#panel-vertical-offset');
      const toggleButton = this.panel.querySelector('#panel-toggle-grid');

      if (colorInput) colorInput.value = settings.color || '#000000';
      if (sizeXInput) sizeXInput.value = settings.gridSizeX || 50;
      if (sizeYInput) sizeYInput.value = settings.gridSizeY || 50;
      if (lockInput) lockInput.checked = settings.lockGridSizes !== undefined ? settings.lockGridSizes : true;
      if (opacityInput) opacityInput.value = settings.opacity || 0.2;
      if (offsetXInput) offsetXInput.value = settings.horizontalOffset || 0;
      if (offsetYInput) offsetYInput.value = settings.verticalOffset || 0;
      if (toggleButton) {
        if (window.gridOverlay) {
          toggleButton.textContent = window.gridOverlay.isVisible ? 'Hide Grid' : 'Show Grid';
        } else {
          toggleButton.textContent = 'Show Grid';
        }
      }

      // Update value displays
      this.updateValueDisplays();
    }
  }

  updateValueDisplays() {
    const displays = [
      { input: '#panel-grid-size-x', display: '#panel-grid-size-x-value' },
      { input: '#panel-grid-size-y', display: '#panel-grid-size-y-value' },
      { input: '#panel-grid-opacity', display: '#panel-grid-opacity-value', isPercent: true },
      { input: '#panel-horizontal-offset', display: '#panel-horizontal-offset-value' },
      { input: '#panel-vertical-offset', display: '#panel-vertical-offset-value' }
    ];

    displays.forEach(({ input, display, isPercent }) => {
      const inputEl = this.panel.querySelector(input);
      const displayEl = this.panel.querySelector(display);
      if (inputEl && displayEl) {
        if (isPercent) {
          displayEl.textContent = Math.round(inputEl.value * 100) + '%';
        } else {
          displayEl.textContent = inputEl.value;
        }
      }
    });
  }
  
  hide() {
    this.isVisible = false;
    this.panel.style.display = 'none';
  }
  
  syncControlsWithGrid() {
    // This will be implemented to sync with the main grid overlay
    // For now, just add basic event listeners
    const controls = {
      color: this.panel.querySelector('#panel-grid-color'),
      sizeX: this.panel.querySelector('#panel-grid-size-x'),
      sizeY: this.panel.querySelector('#panel-grid-size-y'),
      lock: this.panel.querySelector('#panel-lock-grid-sizes'),
      opacity: this.panel.querySelector('#panel-grid-opacity'),
      offsetX: this.panel.querySelector('#panel-horizontal-offset'),
      offsetY: this.panel.querySelector('#panel-vertical-offset'),
      toggle: this.panel.querySelector('#panel-toggle-grid')
    };
    
    // Add event listeners that will communicate with the main grid overlay
    Object.keys(controls).forEach(key => {
      const control = controls[key];
      if (control) {
        const eventType = control.type === 'range' || control.type === 'color' ? 'input' :
                         control.tagName === 'BUTTON' ? 'click' :
                         control.type === 'checkbox' ? 'change' : 'change';
        
        control.addEventListener(eventType, (e) => {
          // Handle different control types
          if (key === 'toggle') {
            // Toggle grid visibility
            console.log('Toggle button clicked, checking gridOverlay...', window.gridOverlay);
            if (window.gridOverlay) {
              console.log('GridOverlay found, calling toggleGrid()');
              const newState = window.gridOverlay.toggleGrid();
              e.target.textContent = newState ? 'Hide Grid' : 'Show Grid';
              console.log('Grid toggled, new state:', newState, 'Button text:', e.target.textContent);
            } else {
              console.error('window.gridOverlay not found! Cannot toggle grid.');
              // Try to initialize gridOverlay if it doesn't exist
              if (typeof initializeGridOverlay === 'function') {
                console.log('Attempting to initialize gridOverlay...');
                initializeGridOverlay();
                // Try again after a short delay
                setTimeout(() => {
                  if (window.gridOverlay) {
                    const newState = window.gridOverlay.toggleGrid();
                    e.target.textContent = newState ? 'Hide Grid' : 'Show Grid';
                  }
                }, 100);
              }
            }
          } else if (key === 'lock') {
            // Handle lock grid sizes
            const isLocked = e.target.checked;
            if (isLocked && window.gridOverlay) {
              // Store the current base values when locking is enabled
              this.lockedBaseX = Number(controls.sizeX.value);
              this.lockedBaseY = Number(controls.sizeY.value);
              console.log(`Lock enabled: Base X=${this.lockedBaseX}, Base Y=${this.lockedBaseY}`);
            }
            // Save lock state
            if (window.gridOverlay) {
              window.gridOverlay.updateSetting('lockGridSizes', isLocked);
            }
          } else {
            // Handle other controls
            const value = control.type === 'checkbox' ? e.target.checked :
                         control.type === 'range' || control.type === 'color' ? e.target.value : e.target.value;

            // Map panel control keys to grid overlay setting keys
            const settingKeyMap = {
              'color': 'color',
              'sizeX': 'gridSizeX',
              'sizeY': 'gridSizeY',
              'opacity': 'opacity',
              'offsetX': 'horizontalOffset',
              'offsetY': 'verticalOffset'
            };

            const settingKey = settingKeyMap[key];
            if (settingKey && window.gridOverlay) {
              const numericValue = control.type === 'range' ? Number(value) : value;
              window.gridOverlay.updateSetting(settingKey, numericValue);

              // Handle lock synchronization
              const lockCheckbox = controls.lock;
              if (lockCheckbox && lockCheckbox.checked) {
                if (key === 'sizeX') {
                  // When X changes, calculate scaling factor and apply to Y
                  const scaleFactor = this.lockedBaseX > 0 ? numericValue / this.lockedBaseX : 1;
                  const newY = Math.round(this.lockedBaseY * scaleFactor);
                  controls.sizeY.value = newY;
                  const valueDisplay = this.panel.querySelector('#panel-grid-size-y-value');
                  if (valueDisplay) valueDisplay.textContent = newY;
                  window.gridOverlay.updateSetting('gridSizeY', newY);
                  console.log(`X changed to ${numericValue}, Y updated to ${newY} (scale factor: ${scaleFactor})`);
                } else if (key === 'sizeY') {
                  // When Y changes, calculate scaling factor and apply to X
                  const scaleFactor = this.lockedBaseY > 0 ? numericValue / this.lockedBaseY : 1;
                  const newX = Math.round(this.lockedBaseX * scaleFactor);
                  controls.sizeX.value = newX;
                  const valueDisplay = this.panel.querySelector('#panel-grid-size-x-value');
                  if (valueDisplay) valueDisplay.textContent = newX;
                  window.gridOverlay.updateSetting('gridSizeX', newX);
                  console.log(`Y changed to ${numericValue}, X updated to ${newX} (scale factor: ${scaleFactor})`);
                }
              }
            }
          }

          // Update value displays for range inputs
          if (control.type === 'range') {
            const valueDisplay = this.panel.querySelector(`#panel-${control.id.replace('panel-', '')}-value`);
            if (valueDisplay) {
              if (key === 'opacity') {
                valueDisplay.textContent = Math.round(e.target.value * 100) + '%';
              } else {
                valueDisplay.textContent = e.target.value;
              }
            }
          }
        });
      }
    });
  }

  // Force panel to stay at 100% scale regardless of browser zoom
  applyZoomCompensation() {
    if (!this.panel) return;

    // Use the ZoomManager from content.js if available
    if (window.ZoomManager) {
      window.ZoomManager.forceNormalScale(this.panel);
    } else {
      // Fallback: Force 100% scale directly
      this.panel.style.setProperty('zoom', '1', 'important');
      this.panel.style.setProperty('transform', 'none', 'important');
      this.panel.style.setProperty('transform-origin', '0 0', 'important');

      // Also apply to all child elements
      const children = this.panel.querySelectorAll('*');
      children.forEach(child => {
        child.style.setProperty('zoom', '1', 'important');
      });

      console.log('Panel forced to 100% scale (fallback method)');
    }
  }

  // Monitor zoom changes and reapply compensation
  startZoomMonitoring() {
    if (this.zoomMonitoringStarted) return;
    this.zoomMonitoringStarted = true;

    let lastZoom = 1;

    const checkZoom = () => {
      // Simple zoom detection
      const testElement = document.createElement('div');
      testElement.style.cssText = 'position: absolute; left: -9999px; width: 100px; height: 100px; zoom: 1;';
      document.body.appendChild(testElement);
      const actualWidth = testElement.offsetWidth;
      document.body.removeChild(testElement);

      const currentZoom = actualWidth / 100;
      if (Math.abs(currentZoom - lastZoom) > 0.01) {
        console.log(`Panel detected zoom change from ${lastZoom} to ${currentZoom}`);
        lastZoom = currentZoom;
        this.applyZoomCompensation();
      }
    };

    // Check on window resize (zoom often triggers resize)
    window.addEventListener('resize', checkZoom);

    // Also check periodically
    setInterval(checkZoom, 1000);
  }
}; // End of DraggableConfigPanel class assignment
} // End of DraggableConfigPanel class definition check

// Global variable to hold the panel instance (only declare if not already exists)
if (!window.draggableConfigPanel) {
  window.draggableConfigPanel = null;
}

// Function to show the draggable config panel (only declare if not already exists)
if (!window.showDraggableConfigPanel) {
  window.showDraggableConfigPanel = function() {
    if (!window.draggableConfigPanel) {
      window.draggableConfigPanel = new window.DraggableConfigPanel();
    }
    window.draggableConfigPanel.show();
  };
}

// Function to hide the draggable config panel (only declare if not already exists)
if (!window.hideDraggableConfigPanel) {
  window.hideDraggableConfigPanel = function() {
    if (window.draggableConfigPanel) {
      window.draggableConfigPanel.hide();
    }
  };
}
