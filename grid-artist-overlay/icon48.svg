<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="highlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#66BB6A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4CAF50;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main background circle -->
  <circle cx="24" cy="24" r="22" fill="url(#bgGradient)"/>
  
  <!-- Inner highlight circle -->
  <circle cx="24" cy="24" r="20" fill="url(#highlightGradient)" opacity="0.3"/>
  
  <!-- Grid container background -->
  <rect x="8" y="8" width="32" height="32" rx="2" fill="rgba(255,255,255,0.1)"/>
  
  <!-- Main grid lines -->
  <g stroke="#ffffff" stroke-width="1.5" opacity="0.9">
    <!-- Vertical lines -->
    <line x1="14" y1="10" x2="14" y2="38"/>
    <line x1="20" y1="10" x2="20" y2="38"/>
    <line x1="24" y1="10" x2="24" y2="38"/>
    <line x1="28" y1="10" x2="28" y2="38"/>
    <line x1="34" y1="10" x2="34" y2="38"/>
    
    <!-- Horizontal lines -->
    <line x1="10" y1="14" x2="38" y2="14"/>
    <line x1="10" y1="20" x2="38" y2="20"/>
    <line x1="10" y1="24" x2="38" y2="24"/>
    <line x1="10" y1="28" x2="38" y2="28"/>
    <line x1="10" y1="34" x2="38" y2="34"/>
  </g>
  
  <!-- Accent grid lines (thicker) -->
  <g stroke="#ffffff" stroke-width="2" opacity="1">
    <line x1="18" y1="12" x2="18" y2="36"/>
    <line x1="30" y1="12" x2="30" y2="36"/>
    <line x1="12" y1="18" x2="36" y2="18"/>
    <line x1="12" y1="30" x2="36" y2="30"/>
  </g>
  
  <!-- Artist brush accent -->
  <g transform="translate(34, 34)">
    <circle cx="0" cy="0" r="6" fill="#FF9800" opacity="0.9"/>
    <circle cx="-1" cy="-1" r="3" fill="#FFC107"/>
  </g>
  
  <!-- Border -->
  <circle cx="24" cy="24" r="21.5" fill="none" stroke="#1B5E20" stroke-width="1"/>
</svg>