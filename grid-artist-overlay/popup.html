
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Artist <PERSON><PERSON> Overlay</title>
  <style>
    /* Force popup to ignore Chrome's View/Zoom controls */
    html, body {
      zoom: 1 !important;
      transform: none !important;
      transform-origin: 0 0 !important;
      -webkit-transform: none !important;
      -moz-transform: none !important;
      -ms-transform: none !important;
      -o-transform: none !important;
    }

    /* Apply zoom independence to all elements */
    *, *:before, *:after {
      zoom: 1 !important;
      transform-origin: 0 0 !important;
    }

    body {
      width: 300px;
      padding: 15px;
      margin: 0;
      font-family: Arial, sans-serif;
      background-color: #f8f9fa;
      /* Force consistent sizing regardless of browser zoom */
      min-width: 300px;
      max-width: 300px;
      box-sizing: border-box;
    }


    .control-group {
      margin-bottom: 15px;
    }

    button {
      width: 100%;
      padding: 10px;
      border: none;
      border-radius: 5px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    #show-panel {
      background-color: #28a745;
      color: white;
    }

    #show-panel:hover {
      background-color: #1e7e34;
    }

    #toggle-grid {
      background-color: #007bff;
      color: white;
    }

    #toggle-grid:hover {
      background-color: #0056b3;
    }
  </style>
</head>
<body>
  <h2>Artist Grid Overlay</h2>

  <div class="control-group">
    <button id="show-panel">Show Grid Settings</button>
  </div>

  <div class="control-group">
    <button id="toggle-grid">Toggle Grid</button>
  </div>

  <p style="font-size: 12px; color: #666; margin-top: 15px;">
    Click "Show Grid Settings" to open the configuration panel on this tab.
  </p>

  <div id="error-message" style="display: none; color: red; margin-top: 10px; font-size: 12px;"></div>

  <script>
    // Force popup to stay at 100% scale regardless of Chrome zoom
    function forcePopupZoomIndependence() {
      // Force zoom reset on document and body
      document.documentElement.style.setProperty('zoom', '1', 'important');
      document.body.style.setProperty('zoom', '1', 'important');
      document.documentElement.style.setProperty('transform', 'none', 'important');
      document.body.style.setProperty('transform', 'none', 'important');

      // Apply to all elements
      const allElements = document.querySelectorAll('*');
      allElements.forEach(element => {
        element.style.setProperty('zoom', '1', 'important');
      });

      console.log('Popup zoom independence applied');
    }

    // Apply immediately when DOM loads
    document.addEventListener('DOMContentLoaded', forcePopupZoomIndependence);

    // Also apply immediately (in case DOMContentLoaded already fired)
    forcePopupZoomIndependence();

    // Monitor for zoom changes and reapply
    let lastWidth = window.innerWidth;
    setInterval(() => {
      if (window.innerWidth !== lastWidth) {
        lastWidth = window.innerWidth;
        forcePopupZoomIndependence();
      }
    }, 500);
  </script>
  <script src="popup.js"></script>
</body>
</html>
