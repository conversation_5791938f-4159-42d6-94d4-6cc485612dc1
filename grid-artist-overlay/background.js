
// Background script for Artist Grid Overlay
// This script runs in the background and handles extension lifecycle events

// Track which tabs have the extension active and their settings
const activeTabs = new Set();
const tabSettings = new Map(); // Store per-tab settings

chrome.runtime.onInstalled.addListener(() => {
  console.log('Artist Grid Overlay extension installed/updated');

  // Initialize default settings
  chrome.storage.sync.set({
    gridSettings: {
      color: '#000000',
      gridSizeX: 50,
      gridSizeY: 50,
      lockGridSizes: true,
      opacity: 0.2,
      horizontalOffset: 0,
      verticalOffset: 0,
      isVisible: false
    }
  });
});

// Handle extension icon clicks
chrome.action.onClicked.addListener(async (tab) => {
  console.log('Extension icon clicked for tab:', tab.id, tab.url);

  try {
    // Check if the URL is valid for content script injection
    if (!isValidUrlForInjection(tab.url)) {
      console.log('Cannot activate extension on this URL:', tab.url);
      // Show a notification or badge to indicate why it can't activate
      chrome.action.setBadgeText({
        tabId: tab.id,
        text: '!'
      });
      chrome.action.setBadgeBackgroundColor({
        tabId: tab.id,
        color: '#ff0000'
      });
      // Clear badge after 3 seconds
      setTimeout(() => {
        chrome.action.setBadgeText({
          tabId: tab.id,
          text: ''
        });
      }, 3000);
      return;
    }

    const isActive = activeTabs.has(tab.id);

    if (isActive) {
      // Deactivate extension on this tab
      await deactivateExtensionOnTab(tab.id);
    } else {
      // Activate extension on this tab
      await activateExtensionOnTab(tab.id, tab.url);
    }
  } catch (error) {
    console.error('Error handling extension icon click:', error);
  }
});

// Check if URL is valid for content script injection
function isValidUrlForInjection(url) {
  if (!url) return false;

  // List of URL patterns that cannot have content scripts injected
  const restrictedPatterns = [
    /^chrome:\/\//,           // Chrome internal pages
    /^chrome-extension:\/\//, // Extension pages
    /^moz-extension:\/\//,    // Firefox extension pages
    /^edge-extension:\/\//,   // Edge extension pages
    /^about:/,                // About pages
    /^data:/,                 // Data URLs
    /^javascript:/,           // JavaScript URLs
    /^mailto:/,               // Mailto links
    /^tel:/,                  // Tel links
    /^ftp:/,                  // FTP (usually not supported)
    /^chrome-search:\/\//,    // Chrome search pages
    /^chrome-devtools:\/\//   // Chrome DevTools
  ];

  return !restrictedPatterns.some(pattern => pattern.test(url));
}

// Activate extension on a specific tab
async function activateExtensionOnTab(tabId, url) {
  try {
    console.log('Activating extension on tab:', tabId, 'URL:', url);

    // Inject content scripts
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['draggable-panel.js', 'content.js']
    });

    // Inject CSS
    await chrome.scripting.insertCSS({
      target: { tabId: tabId },
      files: ['overlay.css']
    });

    // Mark tab as active
    activeTabs.add(tabId);

    // Icon changes removed to avoid decode errors - extension works without them

    // Send message to show the draggable panel
    setTimeout(() => {
      chrome.tabs.sendMessage(tabId, { action: 'showDraggablePanel' }, (response) => {
        if (chrome.runtime.lastError) {
          console.log('Could not send message to tab:', chrome.runtime.lastError.message);
        } else {
          console.log('Extension activated successfully on tab:', tabId);
        }
      });
    }, 500);

  } catch (error) {
    console.error('Failed to activate extension on tab:', tabId, error);
  }
}

// Deactivate extension on a specific tab
async function deactivateExtensionOnTab(tabId) {
  try {
    console.log('Deactivating extension on tab:', tabId);

    // Send message to hide everything
    chrome.tabs.sendMessage(tabId, { action: 'deactivateExtension' }, (response) => {
      if (chrome.runtime.lastError) {
        console.log('Could not send deactivation message:', chrome.runtime.lastError.message);
      }
    });

    // Remove from active tabs
    activeTabs.delete(tabId);

    // Icon reset removed to avoid decode errors

    console.log('Extension deactivated on tab:', tabId);

  } catch (error) {
    console.error('Failed to deactivate extension on tab:', tabId, error);
  }
}



// Monitor tab updates and cleanup
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // When a tab is refreshed or navigated, remove it from active tabs
  if (changeInfo.status === 'loading') {
    if (activeTabs.has(tabId)) {
      console.log('Tab refreshed/navigated, removing from active tabs:', tabId);
      activeTabs.delete(tabId);

      // Icon reset removed to avoid decode errors
    }
  }

  if (changeInfo.status === 'complete' && tab.url) {
    console.log('Tab loaded:', tab.url);

    // For file URLs, provide helpful logging
    if (tab.url.startsWith('file://')) {
      console.log('File URL detected. Extension will work if "Allow access to file URLs" is enabled.');
    }
  }
});

// Clean up when tabs are closed
chrome.tabs.onRemoved.addListener((tabId) => {
  if (activeTabs.has(tabId)) {
    console.log('Tab closed, removing from active tabs:', tabId);
    activeTabs.delete(tabId);
    tabSettings.delete(tabId);
  }
});

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request, 'from:', sender.tab?.url || 'unknown');

  try {
    // Handle saving per-tab settings
    if (request.action === 'saveTabSettings' && sender.tab) {
      const tabId = sender.tab.id;
      tabSettings.set(tabId, request.settings);
      console.log('Saved settings for tab:', tabId, request.settings);
      sendResponse({ success: true });
      return true;
    }

    // Handle loading per-tab settings
    if (request.action === 'loadTabSettings' && sender.tab) {
      const tabId = sender.tab.id;
      const settings = tabSettings.get(tabId);
      console.log('Loaded settings for tab:', tabId, settings);
      sendResponse({ success: true, settings: settings });
      return true;
    }

    // Handle ping requests for debugging
    if (request.action === 'ping') {
      sendResponse({ status: 'success', message: 'Background script responding' });
      return true;
    }

    // Default response
    sendResponse({ status: 'success' });
  } catch (error) {
    console.error('Background script error:', error);
    sendResponse({ status: 'error', message: error.toString() });
  }

  return true; // Allow asynchronous response
});
