<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid Overlay Web Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .instructions {
            background-color: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .test-area {
            background-color: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 40px;
            margin: 20px 0;
            min-height: 400px;
            position: relative;
        }
        .sample-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Artist Grid Overlay - Web Test</h1>
        
        <div class="instructions">
            <h3>📋 Quick Test Instructions:</h3>
            <ol>
                <li>This page works without file URL permissions</li>
                <li>Click the extension icon in the browser toolbar</li>
                <li>Click "Show Grid Settings" to open the configuration panel</li>
                <li>Click the panel header "Grid Overlay Settings" to collapse/expand like an accordion</li>
                <li>Drag the panel by its blue header to move it around (when expanded)</li>
                <li>Click "Toggle Grid" to show/hide the grid overlay</li>
                <li>Test the "Lock Grid Sizes" feature for square grids</li>
                <li>Click "Close Panel" to hide the configuration panel</li>
                <li>Extension only activates per tab when you click the icon</li>
            </ol>
        </div>

        <div class="test-area">
            <h3>Test Area</h3>
            <p>This is a test area where you can verify the grid overlay appears correctly.</p>
            <p>The grid should appear as lines over this content when activated.</p>
            
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPjQwMCB4IDMwMDwvdGV4dD4KICA8IS0tIEdyaWQgbGluZXMgZm9yIHJlZmVyZW5jZSAtLT4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJncmlkIiB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPgogICAgICA8cGF0aCBkPSJNIDUwIDAgTCAwIDAgMCA1MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjY2NjIiBzdHJva2Utd2lkdGg9IjEiLz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNncmlkKSIgb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPg==" 
                 alt="Sample test image with reference grid" 
                 class="sample-image">
        </div>

        <div class="debug-info">
            <strong>Debug Information:</strong><br>
            <span id="debug-output">Page loaded. Extension should inject content script automatically.</span>
        </div>
    </div>

    <script>
        // Debug logging
        function log(message) {
            const debugOutput = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.innerHTML += `<br>[${timestamp}] ${message}`;
            console.log(`[Grid Test] ${message}`);
        }

        // Log page load
        log('Test page loaded successfully');
        log(`Current URL: ${window.location.href}`);
        log('Extension content script should be injecting...');

        // Check if we can detect the grid overlay element
        function checkForGridOverlay() {
            const gridElement = document.getElementById('artist-grid-overlay');
            if (gridElement) {
                log('✅ Grid overlay element detected in DOM');
                log(`Grid display: ${gridElement.style.display}`);
                log(`Grid z-index: ${gridElement.style.zIndex}`);
            } else {
                log('❌ Grid overlay element not found in DOM');
            }
        }

        // Check periodically for the grid overlay
        setTimeout(checkForGridOverlay, 1000);
        setTimeout(checkForGridOverlay, 3000);
        setTimeout(checkForGridOverlay, 5000);

        // Listen for any messages (for debugging)
        window.addEventListener('message', (event) => {
            log(`Received message: ${JSON.stringify(event.data)}`);
        });
    </script>
</body>
</html>
