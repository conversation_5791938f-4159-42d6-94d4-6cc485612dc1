
// Artist Grid Overlay Content Script
// This script runs on web pages to create and manage the grid overlay

// Zoom detection and compensation utilities
if (!window.ZoomManager) {
  window.ZoomManager = {
  currentZoom: 1,

  // Detect current browser zoom level using multiple methods
  detectZoom() {
    // Method 1: Create a test element to measure actual vs expected size
    const testElement = document.createElement('div');
    testElement.style.cssText = 'position: absolute; left: -9999px; top: -9999px; width: 100px; height: 100px; zoom: 1 !important; transform: none !important; box-sizing: content-box; border: none; padding: 0; margin: 0;';
    document.body.appendChild(testElement);
    const actualWidth = testElement.offsetWidth;
    document.body.removeChild(testElement);

    // Calculate zoom (100px expected, actual width tells us zoom)
    const elementZoom = actualWidth / 100;

    // Method 2: Use window.devicePixelRatio for additional validation
    const deviceRatio = window.devicePixelRatio || 1;

    // Method 3: Compare screen dimensions (less reliable but useful for validation)
    const screenZoom = window.screen ? (window.screen.width / window.innerWidth) : 1;

    // Use element measurement as primary method (most accurate)
    this.currentZoom = elementZoom;
    console.log(`Zoom detection - Element: ${elementZoom}, Device: ${deviceRatio}, Screen: ${screenZoom}`);
    return elementZoom;
  },

  // Apply aggressive zoom compensation to force 100% scale
  applyZoomCompensation(element, className = 'zoom-independent') {
    if (!element) return;

    const zoom = this.detectZoom();
    const inverseZoom = 1 / zoom;

    // Apply multiple zoom reset methods for maximum compatibility
    element.classList.add(className);

    // Method 1: CSS zoom property (most direct)
    element.style.zoom = '1';

    // Method 2: Transform scale (backup)
    element.style.transform = `scale(${inverseZoom})`;
    element.style.transformOrigin = '0 0';

    // Method 3: Force important styles
    element.style.setProperty('zoom', '1', 'important');
    element.style.setProperty('transform-origin', '0 0', 'important');

    console.log(`Applied aggressive zoom compensation: ${zoom}x zoom, ${inverseZoom}x scale`);
  },

  // Force 100% scale using proper inverse scaling
  forceNormalScale(element) {
    if (!element) return;

    const zoom = this.detectZoom();
    const inverseZoom = 1 / zoom;

    // Use inverse scaling to counteract browser zoom
    element.style.setProperty('transform', `scale(${inverseZoom})`, 'important');
    element.style.setProperty('transform-origin', '0 0', 'important');

    // Also try CSS zoom as backup
    element.style.setProperty('zoom', '1', 'important');

    // Apply to all child elements to ensure consistent scaling
    const children = element.querySelectorAll('*');
    children.forEach(child => {
      child.style.setProperty('zoom', '1', 'important');
    });

    console.log(`Forced normal scale: browser zoom ${zoom}x, applied scale ${inverseZoom}x`);
  },

  // Remove zoom compensation
  removeZoomCompensation(element, className = 'zoom-independent') {
    if (!element) return;

    element.classList.remove(className);
    element.style.zoom = '';
    element.style.transform = '';
    element.style.transformOrigin = '';
  },

  // Monitor zoom changes
  startZoomMonitoring(callback) {
    let lastZoom = this.detectZoom();

    const checkZoom = () => {
      const currentZoom = this.detectZoom();
      if (Math.abs(currentZoom - lastZoom) > 0.01) {
        console.log(`Zoom changed from ${lastZoom} to ${currentZoom}`);
        lastZoom = currentZoom;
        if (callback) callback(currentZoom);
      }
    };

    // Check on window resize (zoom often triggers resize)
    window.addEventListener('resize', checkZoom);

    // Check on zoom events if available
    window.addEventListener('wheel', (e) => {
      if (e.ctrlKey) {
        setTimeout(checkZoom, 100); // Delay to allow zoom to complete
      }
    });

    // Also check periodically
    setInterval(checkZoom, 2000);

    return checkZoom;
  }
  }; // End of ZoomManager
} // End of ZoomManager check

// Global grid overlay instance (only declare if not already exists)
if (!window.gridOverlay) {
  window.gridOverlay = null;
}

// Check if class already exists to prevent re-declaration errors
if (!window.GridOverlay) {
window.GridOverlay = class GridOverlay {
  constructor() {
    this.gridElement = null;
    this.isVisible = false;
    this.isInitialized = false;
    this.settings = {
      color: '#000000',
      gridSizeX: 50,
      gridSizeY: 50,
      lockGridSizes: true,
      opacity: 0.2,
      horizontalOffset: 0,
      verticalOffset: 0
    };

    this.init();
  }

  // Initialize the grid overlay
  init() {
    this.loadSettings();
    this.createGridElement();
    this.setupImageDropHandling();

    // Handle page navigation and dynamic content
    this.observePageChanges();

    this.isInitialized = true;
  }

  // Load settings from per-tab storage
  loadSettings() {
    try {
      // Check if extension context is still valid
      if (chrome && chrome.runtime) {
        chrome.runtime.sendMessage({
          action: 'loadTabSettings'
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.log('Could not load tab settings:', chrome.runtime.lastError.message);
            return;
          }

          if (response && response.success && response.settings) {
            this.settings = { ...this.settings, ...response.settings };

            // Restore visibility state if it was saved
            if (response.settings.isVisible !== undefined) {
              this.isVisible = response.settings.isVisible;
              if (this.gridElement) {
                this.gridElement.style.display = this.isVisible ? 'block' : 'none';
              }
            }

            this.updateGridStyles();
          }
        });
      }
    } catch (error) {
      // Extension context invalidated - ignore silently
      console.log('Extension context invalidated, cannot load settings');
    }
  }

  // Save settings to per-tab storage
  saveSettings() {
    try {
      // Check if extension context is still valid
      if (chrome && chrome.runtime) {
        chrome.runtime.sendMessage({
          action: 'saveTabSettings',
          settings: this.settings
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.log('Could not save tab settings:', chrome.runtime.lastError.message);
          }
        });
      }
    } catch (error) {
      // Extension context invalidated - ignore silently
      console.log('Extension context invalidated, cannot save settings');
    }
  }

  // Create the grid overlay element
  createGridElement() {
    // Remove existing grid if it exists
    this.removeGridElement();

    // Create new grid element
    this.gridElement = document.createElement('div');
    this.gridElement.id = 'artist-grid-overlay';
    this.gridElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 999999;
      display: none;
    `;

    // Add to document
    document.documentElement.appendChild(this.gridElement);
    this.updateGridStyles();

    // Force 100% scale to keep grid lines at 1px regardless of zoom
    ZoomManager.forceNormalScale(this.gridElement);

    // Monitor zoom changes and reapply compensation
    if (!this.zoomMonitoringStarted) {
      this.zoomMonitoringStarted = true;
      ZoomManager.startZoomMonitoring((newZoom) => {
        if (this.gridElement) {
          ZoomManager.forceNormalScale(this.gridElement);
          // Also update grid styles to maintain proper spacing
          this.updateGridStyles();
        }
      });
    }
  }

  // Remove grid element
  removeGridElement() {
    const existingGrid = document.getElementById('artist-grid-overlay');
    if (existingGrid) {
      existingGrid.remove();
    }
  }

  // Update grid visual styles based on current settings
  updateGridStyles() {
    if (!this.gridElement) return;

    const { color, gridSizeX, gridSizeY, opacity, horizontalOffset, verticalOffset } = this.settings;

    // Create grid pattern using CSS linear gradients
    const backgroundImage = `
      linear-gradient(to right, ${color} 1px, transparent 1px),
      linear-gradient(to bottom, ${color} 1px, transparent 1px)
    `;

    this.gridElement.style.backgroundImage = backgroundImage;
    this.gridElement.style.backgroundSize = `${gridSizeX}px ${gridSizeY}px`;
    this.gridElement.style.backgroundPosition = `${horizontalOffset}px ${verticalOffset}px`;
    this.gridElement.style.opacity = opacity;
  }

  // Toggle grid visibility
  toggleGrid() {
    this.isVisible = !this.isVisible;
    console.log('Grid toggled. New visibility state:', this.isVisible);

    if (this.gridElement) {
      this.gridElement.style.display = this.isVisible ? 'block' : 'none';
      console.log('Grid element display set to:', this.gridElement.style.display);
    } else {
      console.error('Grid element not found when trying to toggle');
    }

    // Save visibility state
    this.settings.isVisible = this.isVisible;
    this.saveSettings();

    return this.isVisible;
  }

  // Update a specific setting
  updateSetting(key, value) {
    this.settings[key] = value;
    this.updateGridStyles();
    this.saveSettings();
  }



  // Observe page changes to maintain grid overlay
  observePageChanges() {
    // Handle dynamic content changes
    const observer = new MutationObserver((_mutations) => {
      // Check if our grid element was removed
      if (!document.getElementById('artist-grid-overlay')) {
        this.createGridElement();
        if (this.isVisible) {
          this.gridElement.style.display = 'block';
        }
      }
    });

    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });

    // Handle page resize
    window.addEventListener('resize', () => {
      this.updateGridStyles();
    });
  }

  // Enhanced image detection for better overlay positioning
  detectImages() {
    const images = document.querySelectorAll('img');
    return Array.from(images).filter(img => {
      return img.complete && img.naturalWidth > 0 && img.naturalHeight > 0;
    });
  }

  // Handle drag and drop events for images
  setupImageDropHandling() {
    document.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    document.addEventListener('drop', (e) => {
      e.preventDefault();

      // Show grid when image is dropped
      if (e.dataTransfer.files.length > 0) {
        const file = e.dataTransfer.files[0];
        if (file.type.startsWith('image/')) {
          // Auto-show grid when image is dropped
          if (!this.isVisible) {
            this.toggleGrid();
          }
        }
      }
    });
  }
}; // End of GridOverlay class assignment
} // End of GridOverlay class definition check

// Set up message listener immediately (before DOM is ready)
try {
  if (chrome && chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  try {
    // If grid overlay isn't initialized yet, wait for it
    if (!gridOverlay || !gridOverlay.isInitialized) {
      // For critical actions, try to initialize immediately
      if (message.action === 'toggleGrid' && !gridOverlay) {
        initializeGridOverlay();
      }

      // If still not ready, return appropriate response
      if (!gridOverlay || !gridOverlay.isInitialized) {
        sendResponse({ success: false, error: 'Grid overlay not ready yet' });
        return true;
      }
    }

    switch (message.action) {
      case 'ping':
        sendResponse({ success: true, message: 'Content script is active', url: window.location.href });
        break;

      case 'toggleGrid':
        const newState = gridOverlay.toggleGrid();
        sendResponse({ success: true, isVisible: newState });
        break;

      case 'updateSetting':
        gridOverlay.updateSetting(message.key, message.value);
        sendResponse({ success: true });
        break;

      case 'getGridState':
        sendResponse({
          success: true,
          isVisible: gridOverlay.isVisible,
          settings: gridOverlay.settings
        });
        break;

      case 'showDraggablePanel':
        if (window.showDraggableConfigPanel) {
          window.showDraggableConfigPanel();
        }
        sendResponse({ success: true, message: 'Draggable panel shown' });
        break;

      case 'hideDraggablePanel':
        if (window.hideDraggableConfigPanel) {
          window.hideDraggableConfigPanel();
        }
        sendResponse({ success: true, message: 'Draggable panel hidden' });
        break;

      case 'deactivateExtension':
        if (window.deactivateExtension) {
          window.deactivateExtension();
        }
        sendResponse({ success: true, message: 'Extension deactivated' });
        break;

      default:
        sendResponse({ success: false, error: 'Unknown action' });
    }
  } catch (error) {
    console.error('Grid overlay error:', error);
    sendResponse({ success: false, error: error.message });
  }

  return true; // Keep message channel open for async response
    });
  }
} catch (error) {
  // Extension context invalidated - ignore silently
  console.log('Extension context invalidated, cannot set up message listener');
}

// Function to initialize grid overlay (only declare if not already exists)
if (!window.initializeGridOverlay) {
  window.initializeGridOverlay = function() {
    try {
      if (!window.gridOverlay) {
        console.log('Initializing Artist Grid Overlay on:', window.location.href);
        window.gridOverlay = new window.GridOverlay();
        console.log('Artist Grid Overlay initialized successfully and exposed to window');
      }
    } catch (error) {
      console.error('Failed to initialize Artist Grid Overlay:', error);
    }
  };
}

// Function to completely deactivate the extension on current tab (only declare if not already exists)
if (!window.deactivateExtension) {
  window.deactivateExtension = function() {
    console.log('Deactivating extension on current tab');

    try {
      // Hide grid if visible
      if (window.gridOverlay && window.gridOverlay.isVisible) {
        window.gridOverlay.toggleGrid();
      }

      // Hide draggable panel
      if (window.hideDraggableConfigPanel) {
        window.hideDraggableConfigPanel();
      }

      // Remove grid element
      if (window.gridOverlay) {
        window.gridOverlay.removeGridElement();
      }

      // Clear global references
      window.gridOverlay = null;

      console.log('Extension deactivated successfully');
    } catch (error) {
      console.error('Error deactivating extension:', error);
    }
  };
}

// Ensure content script is ready to receive messages (only declare if not already exists)
if (!window.ensureContentScriptReady) {
  window.ensureContentScriptReady = function() {
    console.log('Artist Grid Overlay content script loaded');

    // Initialize immediately if DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', window.initializeGridOverlay);
    } else {
      window.initializeGridOverlay();
    }

    // Also try to initialize after a short delay to handle edge cases
    setTimeout(window.initializeGridOverlay, 100);

    // Don't auto-show panel - only show when user clicks extension icon
  };
}

// Start initialization
if (window.ensureContentScriptReady) {
  window.ensureContentScriptReady();
}
