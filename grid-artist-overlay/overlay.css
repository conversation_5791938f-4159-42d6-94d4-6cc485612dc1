
#artist-grid-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
  /* Background image and size will be set dynamically by JavaScript */
  /* to maintain 1px line thickness regardless of zoom level */
  transform-origin: 0 0;
}

/* Zoom-independent styling for extension elements */
.zoom-independent {
  transform-origin: 0 0 !important;
  zoom: 1 !important;
  /* Will be set dynamically by JavaScript */
}

/* Specific zoom independence for draggable panel */
.zoom-independent-panel {
  transform-origin: 0 0 !important;
  zoom: 1 !important;
  /* Force 100% scale regardless of browser zoom */
}

/* Force all extension elements to ignore browser zoom */
#artist-grid-overlay,
#grid-config-panel,
#grid-config-panel * {
  zoom: 1 !important;
  transform-origin: 0 0 !important;
}
