
// Robust message sending with retry logic
function sendMessageToActiveTab(message, retries = 3, delay = 500) {
  return new Promise((resolve, reject) => {
    // Verify we have access to chrome.tabs
    if (!chrome.tabs) {
      reject(new Error('Chrome tabs API not available'));
      return;
    }

    // Query for the active tab
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      // Check for runtime errors
      if (chrome.runtime.lastError) {
        const errorMessage = chrome.runtime.lastError.message || 'Unknown tab query error';
        console.error('Tab query error:', errorMessage);
        reject(new Error(errorMessage));
        return;
      }

      // Validate tabs array
      if (!tabs || tabs.length === 0) {
        reject(new Error('No active tab found'));
        return;
      }

      const activeTab = tabs[0];

      // Send message to the content script
      try {
        chrome.tabs.sendMessage(activeTab.id, message, (response) => {
          // Check for any message sending errors
          if (chrome.runtime.lastError) {
            const errorMessage = chrome.runtime.lastError.message || 'Unknown error';
            console.error('Message sending error:', errorMessage);

            // If this is a connection error and we have retries left, try again
            if (errorMessage.includes('Could not establish connection') && retries > 1) {
              console.log(`Content script not ready, retrying in ${delay}ms... (${retries - 1} attempts left)`);
              setTimeout(() => {
                sendMessageToActiveTab(message, retries - 1, delay * 1.5).then(resolve).catch(reject);
              }, delay);
              return;
            }

            // Provide more helpful error message for connection issues
            if (errorMessage.includes('Could not establish connection')) {
              const helpfulError = new Error('Content script not available. Please refresh the page and try again.');
              helpfulError.originalError = errorMessage;
              reject(helpfulError);
            } else {
              reject(new Error(errorMessage));
            }
            return;
          }

          // Resolve with the response
          resolve(response);
        });
      } catch (error) {
        const errorMessage = error && error.message ? error.message : String(error);
        console.error('Exception in sending message:', errorMessage);
        reject(new Error(errorMessage));
      }
    });
  });
}

// Simple popup functionality (no dragging or minimize)
function initializePopup() {
  // Popup is now just a standard extension popup
  // No special dragging or minimize functionality needed
}

// Initialize popup controls and event listeners
document.addEventListener('DOMContentLoaded', () => {
  // Initialize basic popup functionality
  initializePopup();

  // Get simplified control elements
  const controls = {
    toggleGridButton: document.getElementById('toggle-grid'),
    showPanelButton: document.getElementById('show-panel'),
    errorDisplay: document.getElementById('error-message')
  };

  // Error handling utility
  function displayError(message) {
    if (controls.errorDisplay) {
      controls.errorDisplay.textContent = message;
      controls.errorDisplay.style.display = 'block';
      setTimeout(() => {
        controls.errorDisplay.textContent = '';
        controls.errorDisplay.style.display = 'none';
      }, 3000);
    }
  }

  // Attach event listeners to simplified controls
  function attachEventListeners() {
    // Toggle Grid Button
    controls.toggleGridButton.addEventListener('click', () => {
      console.log('Toggle grid button clicked');
      sendMessageToActiveTab({ action: 'toggleGrid' })
        .then((response) => {
          console.log('Grid toggled successfully:', response);
          if (response && response.success) {
            // Update button text based on grid state
            controls.toggleGridButton.textContent = response.isVisible ? 'Hide Grid' : 'Show Grid';
            // Clear any error messages
            if (controls.errorDisplay) {
              controls.errorDisplay.style.display = 'none';
            }
          }
        })
        .catch((error) => {
          console.error('Toggle grid error:', error);
          // Provide more helpful error messages based on current tab
          chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            const errorMessage = error && error.message ? error.message : String(error);

            if (tabs[0] && tabs[0].url && tabs[0].url.startsWith('file://')) {
              displayError('For file URLs: Go to chrome://extensions/, find this extension, click Details, and enable "Allow access to file URLs"');
            } else if (errorMessage.includes('Could not establish connection')) {
              displayError('Grid overlay not ready. Please refresh the page and try again.');
            } else {
              displayError(`Failed to toggle grid: ${errorMessage}`);
            }
          });
        });
    });

    // Show Panel Button
    controls.showPanelButton.addEventListener('click', () => {
      console.log('Show panel button clicked');
      sendMessageToActiveTab({ action: 'showDraggablePanel' })
        .then((response) => {
          console.log('Panel shown successfully:', response);
          if (response && response.success) {
            // Close the popup after showing the panel
            window.close();
          }
        })
        .catch((error) => {
          console.error('Show panel error:', error);
          // Provide more helpful error messages based on current tab
          chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            const errorMessage = error && error.message ? error.message : String(error);

            if (tabs[0] && tabs[0].url && tabs[0].url.startsWith('file://')) {
              displayError('For file URLs: Go to chrome://extensions/, find this extension, click Details, and enable "Allow access to file URLs"');
            } else if (errorMessage.includes('Content script not available')) {
              displayError('Content script not ready. Please refresh the page and try again.');
            } else if (errorMessage.includes('Could not establish connection')) {
              displayError('Extension not ready on this page. Please refresh and try again.');
            } else {
              displayError(`Failed to show panel: ${errorMessage}`);
            }
          });
        });
    });
  }

  // Query current grid state and update button text
  function updateGridStateDisplay() {
    // First try to ping the content script to see if it's available
    sendMessageToActiveTab({ action: 'ping' })
      .then((response) => {
        console.log('Content script ping successful:', response);
        // If ping works, get the grid state
        return sendMessageToActiveTab({ action: 'getGridState' });
      })
      .then((response) => {
        if (response && response.success) {
          // Update button text based on current grid state
          controls.toggleGridButton.textContent = response.isVisible ? 'Hide Grid' : 'Show Grid';
          // Clear any error messages
          if (controls.errorDisplay) {
            controls.errorDisplay.style.display = 'none';
          }
        }
      })
      .catch((error) => {
        console.log('Content script not available:', error);
        // Set default button text
        controls.toggleGridButton.textContent = 'Show Grid';

        // Show helpful error message
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0] && tabs[0].url && tabs[0].url.startsWith('file://')) {
            displayError('For file URLs: Go to chrome://extensions/, find this extension, click Details, and enable "Allow access to file URLs"');
          } else {
            displayError('Content script not ready. Try refreshing the page.');
          }
        });
      });
  }

  // Initialize simplified popup
  function initPopup() {
    attachEventListeners();
    ensureContentScriptReady();
  }

  // Ensure content script is ready, inject if necessary
  function ensureContentScriptReady() {
    // Try to ping the content script first
    sendMessageToActiveTab({ action: 'ping' }, 1, 100) // Single quick attempt
      .then((response) => {
        console.log('Content script is ready:', response);
        updateGridStateDisplay();
      })
      .catch((error) => {
        console.log('Content script not ready, attempting injection...');

        // Try to inject content script
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0]) {
            const tabId = tabs[0].id;

            // Try to inject the content script
            chrome.scripting.executeScript({
              target: { tabId: tabId },
              files: ['draggable-panel.js', 'content.js']
            }).then(() => {
              console.log('Content script injected successfully');
              // Wait a moment then try to update display
              setTimeout(() => {
                updateGridStateDisplay();
              }, 500);
            }).catch((injectionError) => {
              console.log('Could not inject content script:', injectionError);
              // Show appropriate error message
              if (tabs[0].url && tabs[0].url.startsWith('file://')) {
                displayError('For file URLs: Enable "Allow access to file URLs" in extension settings');
              } else {
                displayError('Extension not ready. Please refresh the page and try again.');
              }
            });
          }
        });
      });
  }

  // Start initialization
  initPopup();
});
