<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 250px;
            padding: 20px;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.2s;
            color: white;
        }
        
        .primary-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }
        
        .primary-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .secondary-btn {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }
        
        .secondary-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        
        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 6px;
            font-size: 11px;
            line-height: 1.4;
            margin-top: 15px;
        }
        
        .shortcut {
            font-weight: 600;
            color: #FFE082;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Artist Grid Overlay</div>
        <div class="subtitle">Drawing Proportion Helper</div>
    </div>
    
    <button id="togglePanel" class="button primary-btn">
        Open Control Panel
    </button>
    
    <button id="toggleGrid" class="button secondary-btn">
        Toggle Grid
    </button>
    
    <div class="info">
        <strong>Quick Tips:</strong><br>
        • Drag the control panel anywhere on screen<br>
        • Grid appears over all images and content<br>
        • Keyboard shortcut: <span class="shortcut">Ctrl+Shift+G</span><br>
        • Settings auto-save across pages
    </div>

    <script src="popup.js"></script>
</body>
</html>