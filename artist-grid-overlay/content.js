// Artist Grid Overlay - Content Script

class ArtistGridOverlay {
  constructor() {
    this.isInitialized = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.settings = {
      gridColor: '#ff0000',
      gridSizeX: 50,
      gridSizeY: 50,
      gridOffsetX: 0,
      gridOffsetY: 0,
      opacity: 0.7,
      isVisible: false,
      panelVisible: false
    };
    
    this.init();
  }

  async init() {
    if (this.isInitialized) return;
    
    // Load saved settings
    await this.loadSettings();
    
    // Create overlay elements
    this.createOverlay();
    this.createControlPanel();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Apply initial settings
    this.updateGrid();
    
    this.isInitialized = true;
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(['artistGridSettings']);
      if (result.artistGridSettings) {
        this.settings = { ...this.settings, ...result.artistGridSettings };
      }
    } catch (error) {
      console.log('Using default settings');
    }
  }

  async saveSettings() {
    try {
      await chrome.storage.sync.set({ artistGridSettings: this.settings });
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  createOverlay() {
    // Remove existing overlay if present
    const existing = document.getElementById('artist-grid-overlay');
    if (existing) existing.remove();

    const overlay = document.createElement('div');
    overlay.id = 'artist-grid-overlay';
    overlay.innerHTML = '<div class="grid-lines"></div>';
    
    if (this.settings.isVisible) {
      overlay.classList.add('active');
    }

    document.body.appendChild(overlay);
    this.overlay = overlay;
  }

  createControlPanel() {
    // Remove existing panel if present
    const existing = document.getElementById('artist-grid-panel');
    if (existing) existing.remove();

    const panel = document.createElement('div');
    panel.id = 'artist-grid-panel';
    panel.innerHTML = `
      <div class="panel-header">
        <div class="panel-title">Artist Grid</div>
        <button class="close-btn" id="close-panel">×</button>
      </div>
      
      <div class="control-group">
        <label class="control-label">Grid Color</label>
        <div class="color-picker-wrapper">
          <input type="color" id="grid-color" value="${this.settings.gridColor}">
          <div class="color-preview" style="background-color: ${this.settings.gridColor}"></div>
          <span style="font-size: 12px;">${this.settings.gridColor}</span>
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Grid Size X</label>
        <div class="control-row">
          <input type="range" id="grid-size-x" min="10" max="200" value="${this.settings.gridSizeX}">
          <input type="number" id="grid-size-x-num" min="10" max="200" value="${this.settings.gridSizeX}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Grid Size Y</label>
        <div class="control-row">
          <input type="range" id="grid-size-y" min="10" max="200" value="${this.settings.gridSizeY}">
          <input type="number" id="grid-size-y-num" min="10" max="200" value="${this.settings.gridSizeY}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Horizontal Offset</label>
        <div class="control-row">
          <input type="range" id="grid-offset-x" min="-100" max="100" value="${this.settings.gridOffsetX}">
          <input type="number" id="grid-offset-x-num" min="-100" max="100" value="${this.settings.gridOffsetX}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Vertical Offset</label>
        <div class="control-row">
          <input type="range" id="grid-offset-y" min="-100" max="100" value="${this.settings.gridOffsetY}">
          <input type="number" id="grid-offset-y-num" min="-100" max="100" value="${this.settings.gridOffsetY}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Opacity</label>
        <div class="opacity-control">
          <input type="range" id="opacity" min="0.1" max="1" step="0.1" value="${this.settings.opacity}">
          <span class="opacity-value">${Math.round(this.settings.opacity * 100)}%</span>
        </div>
      </div>

      <button class="toggle-grid" id="toggle-grid">
        ${this.settings.isVisible ? 'Hide Grid' : 'Show Grid'}
      </button>
    `;

    if (this.settings.panelVisible) {
      panel.classList.add('visible');
    }

    document.body.appendChild(panel);
    this.panel = panel;
  }

  setupEventListeners() {
    // Panel dragging
    const header = this.panel.querySelector('.panel-header');
    header.addEventListener('mousedown', (e) => this.startDrag(e));
    document.addEventListener('mousemove', (e) => this.drag(e));
    document.addEventListener('mouseup', () => this.stopDrag());

    // Close panel
    document.getElementById('close-panel').addEventListener('click', () => {
      this.settings.panelVisible = false;
      this.panel.classList.remove('visible');
      this.saveSettings();
    });

    // Toggle grid
    document.getElementById('toggle-grid').addEventListener('click', () => {
      this.toggleGrid();
    });

    // Color picker
    const colorPicker = document.getElementById('grid-color');
    colorPicker.addEventListener('input', (e) => {
      this.settings.gridColor = e.target.value;
      this.updateGrid();
      this.updateColorPreview();
      this.saveSettings();
    });

    // Grid size controls
    this.setupRangeControl('grid-size-x', 'gridSizeX');
    this.setupRangeControl('grid-size-y', 'gridSizeY');
    this.setupRangeControl('grid-offset-x', 'gridOffsetX');
    this.setupRangeControl('grid-offset-y', 'gridOffsetY');

    // Opacity control
    const opacityRange = document.getElementById('opacity');
    opacityRange.addEventListener('input', (e) => {
      this.settings.opacity = parseFloat(e.target.value);
      this.updateGrid();
      this.updateOpacityDisplay();
      this.saveSettings();
    });

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'togglePanel') {
        this.togglePanel();
        sendResponse({ success: true });
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'G') {
        e.preventDefault();
        this.togglePanel();
      }
    });
  }

  setupRangeControl(id, settingKey) {
    const range = document.getElementById(id);
    const number = document.getElementById(id + '-num');

    range.addEventListener('input', (e) => {
      const value = parseInt(e.target.value);
      this.settings[settingKey] = value;
      number.value = value;
      this.updateGrid();
      this.saveSettings();
    });

    number.addEventListener('input', (e) => {
      const value = parseInt(e.target.value);
      this.settings[settingKey] = value;
      range.value = value;
      this.updateGrid();
      this.saveSettings();
    });
  }

  startDrag(e) {
    this.isDragging = true;
    this.dragOffset.x = e.clientX - this.panel.offsetLeft;
    this.dragOffset.y = e.clientY - this.panel.offsetTop;
    this.panel.style.cursor = 'grabbing';
  }

  drag(e) {
    if (!this.isDragging) return;
    
    e.preventDefault();
    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;
    
    // Keep panel within viewport
    const maxX = window.innerWidth - this.panel.offsetWidth;
    const maxY = window.innerHeight - this.panel.offsetHeight;
    
    this.panel.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
    this.panel.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
    this.panel.style.right = 'auto';
  }

  stopDrag() {
    this.isDragging = false;
    if (this.panel) {
      this.panel.style.cursor = 'default';
    }
  }

  updateGrid() {
    if (!this.overlay) return;

    const gridLines = this.overlay.querySelector('.grid-lines');
    gridLines.style.setProperty('--grid-color', this.settings.gridColor);
    gridLines.style.setProperty('--grid-size-x', this.settings.gridSizeX + 'px');
    gridLines.style.setProperty('--grid-size-y', this.settings.gridSizeY + 'px');
    gridLines.style.setProperty('--grid-offset-x', this.settings.gridOffsetX + 'px');
    gridLines.style.setProperty('--grid-offset-y', this.settings.gridOffsetY + 'px');
    
    this.overlay.style.opacity = this.settings.opacity;
  }

  updateColorPreview() {
    const preview = this.panel.querySelector('.color-preview');
    const colorText = this.panel.querySelector('.color-picker-wrapper span');
    if (preview && colorText) {
      preview.style.backgroundColor = this.settings.gridColor;
      colorText.textContent = this.settings.gridColor;
    }
  }

  updateOpacityDisplay() {
    const opacityValue = this.panel.querySelector('.opacity-value');
    if (opacityValue) {
      opacityValue.textContent = Math.round(this.settings.opacity * 100) + '%';
    }
  }

  toggleGrid() {
    this.settings.isVisible = !this.settings.isVisible;
    
    if (this.settings.isVisible) {
      this.overlay.classList.add('active');
    } else {
      this.overlay.classList.remove('active');
    }

    const toggleBtn = document.getElementById('toggle-grid');
    toggleBtn.textContent = this.settings.isVisible ? 'Hide Grid' : 'Show Grid';
    toggleBtn.classList.toggle('active', this.settings.isVisible);

    this.saveSettings();
  }

  togglePanel() {
    this.settings.panelVisible = !this.settings.panelVisible;
    
    if (this.settings.panelVisible) {
      this.panel.classList.add('visible');
    } else {
      this.panel.classList.remove('visible');
    }

    this.saveSettings();
  }

  showPanel() {
    this.settings.panelVisible = true;
    this.panel.classList.add('visible');
    this.saveSettings();
  }
}

// Initialize the overlay when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ArtistGridOverlay();
  });
} else {
  new ArtistGridOverlay();
}