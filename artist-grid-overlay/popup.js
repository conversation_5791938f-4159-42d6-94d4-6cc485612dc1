// Popup script for Artist Grid Overlay

document.addEventListener('DOMContentLoaded', async function() {
    const togglePanelBtn = document.getElementById('togglePanel');
    const toggleGridBtn = document.getElementById('toggleGrid');

    // Get current tab
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    // Toggle control panel
    togglePanelBtn.addEventListener('click', async () => {
        try {
            await chrome.tabs.sendMessage(tab.id, { action: 'togglePanel' });
            window.close();
        } catch (error) {
            console.error('Error toggling panel:', error);
            // If content script isn't loaded, inject it
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content.js']
                });
                await chrome.scripting.insertCSS({
                    target: { tabId: tab.id },
                    files: ['overlay.css']
                });
                // Try again after injection
                setTimeout(async () => {
                    try {
                        await chrome.tabs.sendMessage(tab.id, { action: 'togglePanel' });
                        window.close();
                    } catch (e) {
                        console.error('Still failed after injection:', e);
                    }
                }, 100);
            } catch (injectionError) {
                console.error('Failed to inject content script:', injectionError);
            }
        }
    });

    // Toggle grid (this would need to be implemented in content script)
    toggleGridBtn.addEventListener('click', async () => {
        try {
            await chrome.tabs.sendMessage(tab.id, { action: 'toggleGrid' });
            window.close();
        } catch (error) {
            console.error('Error toggling grid:', error);
        }
    });

    // Load current settings to update button states
    try {
        const result = await chrome.storage.sync.get(['artistGridSettings']);
        if (result.artistGridSettings) {
            const settings = result.artistGridSettings;
            if (settings.panelVisible) {
                togglePanelBtn.textContent = 'Close Control Panel';
            }
            if (settings.isVisible) {
                toggleGridBtn.textContent = 'Hide Grid';
            }
        }
    } catch (error) {
        console.error('Error loading settings:', error);
    }
});
