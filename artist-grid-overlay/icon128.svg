<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- Complex gradient definitions -->
  <defs>
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#66BB6A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="highlightGradient" cx="30%" cy="30%">
      <stop offset="0%" style="stop-color:#81C784;stop-opacity:0.8" />
      <stop offset="70%" style="stop-color:#4CAF50;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:0" />
    </radialGradient>
    <linearGradient id="gridBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.15" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.05" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#1B5E20" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Main background circle with shadow -->
  <circle cx="64" cy="64" r="58" fill="url(#mainGradient)" filter="url(#shadow)"/>
  
  <!-- Highlight overlay -->
  <circle cx="64" cy="64" r="56" fill="url(#highlightGradient)"/>
  
  <!-- Grid container background -->
  <rect x="20" y="20" width="88" height="88" rx="6" fill="url(#gridBgGradient)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  
  <!-- Fine grid lines -->
  <g stroke="#ffffff" stroke-width="1" opacity="0.6">
    <!-- Vertical fine lines -->
    <line x1="28" y1="24" x2="28" y2="104"/>
    <line x1="36" y1="24" x2="36" y2="104"/>
    <line x1="44" y1="24" x2="44" y2="104"/>
    <line x1="52" y1="24" x2="52" y2="104"/>
    <line x1="60" y1="24" x2="60" y2="104"/>
    <line x1="68" y1="24" x2="68" y2="104"/>
    <line x1="76" y1="24" x2="76" y2="104"/>
    <line x1="84" y1="24" x2="84" y2="104"/>
    <line x1="92" y1="24" x2="92" y2="104"/>
    <line x1="100" y1="24" x2="100" y2="104"/>
    
    <!-- Horizontal fine lines -->
    <line x1="24" y1="28" x2="104" y2="28"/>
    <line x1="24" y1="36" x2="104" y2="36"/>
    <line x1="24" y1="44" x2="104" y2="44"/>
    <line x1="24" y1="52" x2="104" y2="52"/>
    <line x1="24" y1="60" x2="104" y2="60"/>
    <line x1="24" y1="68" x2="104" y2="68"/>
    <line x1="24" y1="76" x2="104" y2="76"/>
    <line x1="24" y1="84" x2="104" y2="84"/>
    <line x1="24" y1="92" x2="104" y2="92"/>
    <line x1="24" y1="100" x2="104" y2="100"/>
  </g>
  
  <!-- Medium grid lines -->
  <g stroke="#ffffff" stroke-width="1.5" opacity="0.8">
    <!-- Vertical medium lines -->
    <line x1="40" y1="24" x2="40" y2="104"/>
    <line x1="56" y1="24" x2="56" y2="104"/>
    <line x1="72" y1="24" x2="72" y2="104"/>
    <line x1="88" y1="24" x2="88" y2="104"/>
    
    <!-- Horizontal medium lines -->
    <line x1="24" y1="40" x2="104" y2="40"/>
    <line x1="24" y1="56" x2="104" y2="56"/>
    <line x1="24" y1="72" x2="104" y2="72"/>
    <line x1="24" y1="88" x2="104" y2="88"/>
  </g>
  
  <!-- Major grid lines -->
  <g stroke="#ffffff" stroke-width="2.5" opacity="1">
    <line x1="48" y1="26" x2="48" y2="102"/>
    <line x1="64" y1="26" x2="64" y2="102"/>
    <line x1="80" y1="26" x2="80" y2="102"/>
    <line x1="26" y1="48" x2="102" y2="48"/>
    <line x1="26" y1="64" x2="102" y2="64"/>
    <line x1="26" y1="80" x2="102" y2="80"/>
  </g>
  
  <!-- Artist tools accent -->
  <g transform="translate(90, 90)">
    <!-- Brush -->
    <circle cx="0" cy="0" r="12" fill="#FF9800" opacity="0.9"/>
    <circle cx="-2" cy="-2" r="8" fill="#FFC107"/>
    <path d="M -4,-4 L 4,4 M -4,4 L 4,-4" stroke="#FF6F00" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- Pencil accent -->
  <g transform="translate(38, 90)">
    <circle cx="0" cy="0" r="8" fill="#2196F3" opacity="0.8"/>
    <circle cx="-1" cy="-1" r="5" fill="#42A5F5"/>
    <rect x="-2" y="-2" width="4" height="4" fill="#1976D2" rx="1"/>
  </g>
  
  <!-- Corner indicators -->
  <g opacity="0.7">
    <circle cx="24" cy="24" r="2" fill="#ffffff"/>
    <circle cx="104" cy="24" r="2" fill="#ffffff"/>
    <circle cx="24" cy="104" r="2" fill="#ffffff"/>
    <circle cx="104" cy="104" r="2" fill="#ffffff"/>
  </g>
  
  <!-- Outer border -->
  <circle cx="64" cy="64" r="57" fill="none" stroke="#1B5E20" stroke-width="2"/>
  
  <!-- Inner border highlight -->
  <circle cx="64" cy="64" r="55" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
</svg>