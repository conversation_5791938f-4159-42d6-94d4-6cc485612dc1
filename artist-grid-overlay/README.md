# Artist Grid Overlay - Chrome Extension

A professional Chrome extension designed for digital artists to overlay customizable grids on web pages, helping with drawing proportions and composition analysis.

## Features

### 🎨 **Visual Grid Overlay**
- Semi-transparent grid that overlays on top of all content
- Perfect for analyzing proportions in reference images
- Non-intrusive design that doesn't interfere with page interaction

### 🎛️ **Comprehensive Controls**
- **Color Picker**: Change grid line color with live preview
- **Grid Dimensions**: Separate X and Y axis sizing (10-200px range)
- **Positioning**: Fine-tune grid offset horizontally and vertically
- **Opacity Control**: Adjust transparency from 10% to 100%
- **Real-time Updates**: All changes apply instantly

### 🖱️ **Draggable Interface**
- Floating control panel that can be positioned anywhere
- Smooth drag functionality with viewport boundaries
- Collapsible panel to maximize workspace
- Keyboard shortcut support (Ctrl+Shift+G)

### 💾 **Smart Persistence**
- Settings automatically save across browser sessions
- Per-domain memory of grid preferences
- Instant restoration when revisiting pages

## Installation Instructions

### Method 1: Developer Mode Installation

1. **Download the Extension Files**
   - Save all the provided files in a folder named `artist-grid-overlay`
   - Required files:
     - `manifest.json`
     - `content.js`
     - `overlay.css`
     - `popup.html`
     - `popup.js`

2. **Enable Developer Mode**
   - Open Chrome and navigate to `chrome://extensions/`
   - Toggle "Developer mode" in the top-right corner

3. **Load the Extension**
   - Click "Load unpacked"
   - Select the `artist-grid-overlay` folder
   - The extension should now appear in your extensions list

4. **Pin the Extension** (Optional)
   - Click the puzzle piece icon in Chrome's toolbar
   - Find "Artist Grid Overlay" and click the pin icon

### Method 2: Create Icon Files (Optional)

For a complete installation, create simple icon files:

- **icon16.png**: 16x16 pixels
- **icon48.png**: 48x48 pixels  
- **icon128.png**: 128x128 pixels

Place these in the same folder as the other files.

## How to Use

### Basic Operation

1. **Activate the Extension**
   - Click the extension icon in Chrome's toolbar
   - Or use the keyboard shortcut: `Ctrl+Shift+G`

2. **Open Control Panel**
   - Click "Open Control Panel" in the popup
   - The floating panel will appear on the right side of your screen

3. **Show/Hide Grid**
   - Click the "Show Grid" button to display the overlay
   - Grid will appear across the entire viewport

### Customization Options

#### **Grid Color**
- Use the color picker to select any color
- Live preview shows the current selection
- Hex code is displayed for reference

#### **Grid Dimensions**
- **Grid Size X**: Horizontal spacing between lines
- **Grid Size Y**: Vertical spacing between lines
- Use sliders or input exact pixel values (10-200px range)

#### **Grid Positioning**
- **Horizontal Offset**: Move grid left/right (-100 to +100px)
- **