<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Grid Overlay - Standalone</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-controls button {
            margin: 5px;
            padding: 10px 15px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-controls button:hover {
            background: #005a87;
        }
        .image-container {
            position: relative;
            max-width: 90vw;
            max-height: 70vh;
        }
        img {
            max-width: 100%;
            max-height: 100%;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
    </style>
    <!-- Include the extension's CSS -->
    <link rel="stylesheet" href="artist-overlay-grid-v2/overlay.css">
</head>
<body>
    <div class="test-controls">
        <h3>Grid Overlay Test</h3>
        <button onclick="toggleGrid()">Toggle Grid</button>
        <button onclick="showPanel()">Show Panel</button>
        <button onclick="testFileContext()">Test File Context</button>
        <button onclick="debugOverlay()">Debug Overlay</button>
    </div>
    
    <div class="image-container">
        <img src="https://picsum.photos/800/600" alt="Test Image for Grid Overlay" />
    </div>

    <!-- Include the extension's JavaScript -->
    <script src="artist-overlay-grid-v2/content.js"></script>
    
    <script>
        // Test functions
        let gridOverlay = null;
        
        // Initialize the grid overlay when page loads
        window.addEventListener('DOMContentLoaded', async function() {
            console.log('Initializing grid overlay test...');
            
            // Simulate file context for testing
            window.location.protocol = 'file:';
            
            // Create and initialize the grid overlay
            gridOverlay = new ArtistGridOverlay();
            await gridOverlay.safeInit();
            
            console.log('Grid overlay initialized:', gridOverlay);
        });
        
        function toggleGrid() {
            if (gridOverlay) {
                gridOverlay.toggleGrid();
                console.log('Grid toggled');
            } else {
                console.log('Grid overlay not initialized');
            }
        }
        
        function showPanel() {
            if (gridOverlay) {
                gridOverlay.showPanel();
                console.log('Panel shown');
            } else {
                console.log('Grid overlay not initialized');
            }
        }
        
        function testFileContext() {
            if (gridOverlay) {
                // Force file context detection
                gridOverlay.isFileContext = {
                    isFile: true,
                    isImage: true,
                    isVideo: false,
                    isPDF: false,
                    hasMedia: true,
                    isFileURL: true,
                    decodedPath: '/test/image.jpg'
                };
                
                // Recreate overlay with file context
                if (gridOverlay.overlay) {
                    gridOverlay.overlay.remove();
                }
                gridOverlay.createOverlay();
                gridOverlay.showGrid();
                
                console.log('File context applied:', gridOverlay.isFileContext);
            }
        }
        
        function debugOverlay() {
            if (gridOverlay) {
                const overlay = document.getElementById('artist-grid-overlay');
                const panel = document.getElementById('artist-grid-panel');
                
                console.log('Debug Info:', {
                    overlayExists: !!overlay,
                    overlayClasses: overlay ? overlay.className : 'none',
                    overlayStyle: overlay ? overlay.style.cssText : 'none',
                    panelExists: !!panel,
                    isFileContext: gridOverlay.isFileContext,
                    settings: gridOverlay.settings
                });
                
                if (overlay) {
                    const gridLines = overlay.querySelector('.grid-lines');
                    console.log('Grid Lines:', {
                        exists: !!gridLines,
                        style: gridLines ? gridLines.style.cssText : 'none',
                        computedStyle: gridLines ? window.getComputedStyle(gridLines) : 'none'
                    });
                }
            }
        }
    </script>
</body>
</html>
