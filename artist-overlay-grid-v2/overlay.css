/* Artist Grid Overlay Styles - Enhanced for File Support */
#artist-grid-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 10000;
  opacity: 0.7;
  display: none;
}

#artist-grid-overlay.active {
  display: block !important;
}

/* Enhanced styling for file contexts */
#artist-grid-overlay.file-context {
  background: transparent;
  z-index: 2147483647; /* Maximum z-index value */
}

/* Ensure grid works over images, videos, and PDFs */
body img, body video, body embed {
  position: relative;
}

/* Special handling for Chrome's default file viewer */
body {
  position: relative;
}

/* Ensure overlay appears over Chrome's built-in image viewer */
#artist-grid-overlay.file-context.active {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2147483647 !important; /* Maximum possible z-index */
  display: block !important;
  opacity: 1 !important;
}

/* Force overlay to be on top of everything in Chrome's image viewer */
html[data-color-scheme] #artist-grid-overlay.file-context.active,
html #artist-grid-overlay.file-context.active {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2147483647 !important;
  display: block !important;
  opacity: 1 !important;
  transform: none !important;
}

.grid-lines {
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, var(--grid-color, #ff0000) 1px, transparent 1px),
    linear-gradient(to bottom, var(--grid-color, #ff0000) 1px, transparent 1px);
  background-size: var(--grid-size-x, 50px) var(--grid-size-y, 50px);
  background-position: var(--grid-offset-x, 0px) var(--grid-offset-y, 0px);
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
}

/* Ensure grid lines are visible in file contexts */
#artist-grid-overlay.file-context .grid-lines {
  background-image:
    linear-gradient(to right, var(--grid-color, #ff0000) 2px, transparent 2px),
    linear-gradient(to bottom, var(--grid-color, #ff0000) 2px, transparent 2px);
  opacity: 1 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: inherit;
}

/* Control Panel Styles */
#artist-grid-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-radius: 12px;
  padding: 20px;
  z-index: 10001;
  pointer-events: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #ecf0f1;
  display: none;
}

#artist-grid-panel.visible {
  display: block;
}

/* File context indicator */
.file-context-indicator {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  cursor: move;
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #3498db;
}

.close-btn {
  background: #e74c3c;
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.close-btn:hover {
  background: #c0392b;
}

.control-group {
  margin-bottom: 15px;
}

.control-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #bdc3c7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.control-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

input[type="range"] {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #3498db;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.2s;
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: #2980b9;
}

input[type="number"] {
  width: 60px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: #ecf0f1;
  font-size: 12px;
  text-align: center;
}

input[type="number"]:focus {
  outline: none;
  border-color: #3498db;
  background: rgba(255, 255, 255, 0.15);
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

input[type="color"] {
  width: 40px;
  height: 30px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  background: none;
  padding: 0;
}

.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.toggle-grid {
  width: 100%;
  padding: 10px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border: none;
  border-radius: 6px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 10px;
}

.toggle-grid:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.toggle-grid.active {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.opacity-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.opacity-value {
  font-size: 12px;
  font-weight: 600;
  color: #3498db;
  min-width: 35px;
}

/* Preset buttons for file contexts */
.preset-btn {
  width: 48%;
  padding: 8px;
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 5px;
}

.preset-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(155, 89, 182, 0.3);
}

.preset-btn:first-child {
  margin-right: 4%;
}

/* Enhanced styles for file viewing */
body.file-view #artist-grid-panel {
  backdrop-filter: blur(10px);
  background: rgba(44, 62, 80, 0.95);
}

/* Ensure grid appears over file content */
body > img,
body > video,
body > embed[type="application/pdf"] {
  position: relative;
  z-index: 1;
}

#artist-grid-overlay {
  z-index: 9999;
}

#artist-grid-panel {
  z-index: 10001;
}

/* Handle Chrome's default image centering */
body > img {
  display: block;
  margin: auto;
  max-width: 100%;
  max-height: 100%;
}

/* Special handling for PDF viewer */
embed[type="application/pdf"] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}